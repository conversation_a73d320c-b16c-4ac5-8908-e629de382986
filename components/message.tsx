"use client";

import type { ChatRequestOptions } from "ai";
import type { ExtendedMessage } from "@/lib/types";
import cx from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { memo, useMemo, useState, useRef, useEffect } from "react";
import { marked } from "marked";
import { useCopyToClipboard } from "usehooks-ts";
import React from "react";
import { formatDistanceToNow, format } from "date-fns";

import type { Vote } from "@/lib/db/schema";

import { DocumentToolCall, DocumentToolResult } from "./document";
import {
  PencilEditIcon,
  SparklesIcon,
  SparklingSparkleIcon,
  CopyIcon,
  LogoIqidis,
} from "./icons";
import { Markdown } from "./markdown";
import { MarkdownWithInteractiveCitations } from "./markdown-with-interactive-citations";
import { MessageActions } from "./message-actions";
import { PreviewAttachment } from "./preview-attachment";
import { Weather } from "./weather";
import equal from "fast-deep-equal";
import { cn } from "@/lib/utils";
import { Button } from "./ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { MessageEditor } from "./message-editor";
import { DocumentPreview } from "./document-preview";
import { toast } from "sonner";
import { RewrittenQueryDisplay } from "./rewritten-query-display";
import { Logger } from "@/lib/utils/Logger";
import { ErrorMessage } from "./error-message";
import { fixMessageContent } from "@/lib/formatting/utils";
<<<<<<< HEAD
import { CitationBadge } from "./citation-status-indicator";
=======
import { CitationVerificationDisplay } from "./citation-verification-display";
>>>>>>> development

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
  onRetry,
}: {
  chatId: string;
  message: ExtendedMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: (
    messages:
      | ExtendedMessage[]
      | ((messages: ExtendedMessage[]) => ExtendedMessage[])
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  onRetry?: (userMessageId?: string) => void;
}) => {
  const [mode, setMode] = useState<"view" | "edit">("view");
  const [showCopyTooltip, setShowCopyTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const markdownRef = useRef<HTMLDivElement>(null);
  const [_, copyToClipboard] = useCopyToClipboard();

  // Check if this is an error message
  const isErrorMessage = Boolean(message.metadata?.error);

  // Citation status (now synchronous - no polling needed)
  const citationCount = message.metadata?.inlineCitations?.citationCount || 0;
  const citationEnabled = message.metadata?.inlineCitations?.enabled || false;
  const citationTriggered = message.metadata?.inlineCitations?.triggered || false;

  // Ensure citations are properly typed and structured
  const rawCitations = message.metadata?.inlineCitations?.citations || [];
  const citations = rawCitations.map((citation: any) => {
    // Ensure each citation has the required properties
    if (!citation || typeof citation !== 'object') {
      return null;
    }

    return {
      chunkId: citation.chunkId || '',
      similarity: typeof citation.similarity === 'number' ? citation.similarity : 0,
      chunkText: citation.chunkText || '',
      chunkPreview: citation.chunkPreview || citation.chunkText || '',
      pageNumber: citation.pageNumber || 0,
      sourceDocumentId: citation.sourceDocumentId || '',
      filename: citation.filename || 'Unknown Document'
    };
  }).filter(Boolean); // Remove any null entries

  // Enhanced logging for debugging citation status
  useEffect(() => {
    if (message.role === "assistant") {
      Logger.info("Message citation status", {
        messageId: message.id,
        citationTriggered,
        citationEnabled,
        citationCount,
        citationsArrayLength: citations.length,
        hasMetadata: !!message.metadata,
        hasInlineCitations: !!message.metadata?.inlineCitations,
        citationData: citations.map((c: any, i: number) => ({
          index: i + 1,
          filename: c?.filename,
          pageNumber: c?.pageNumber,
          chunkId: c?.chunkId,
          hasPreview: !!c?.chunkPreview
        })),
        metadata: message.metadata?.inlineCitations
      });

      // Additional debugging for citation data structure (development only)
      if (process.env.NODE_ENV === 'development') {
        console.log("🔍 Message citations debug:", {
          messageId: message.id,
          contentPreview: typeof message.content === 'string' ? message.content.substring(0, 100) + '...' : 'Not string',
          hasContent: !!message.content,
          citationCount,
          rawCitationsLength: rawCitations.length,
          processedCitationsLength: citations.length,
          rawCitations: rawCitations,
          processedCitations: citations,
          citationTypes: citations.map((c: any) => typeof c),
          citationKeys: citations.map((c: any) => c ? Object.keys(c) : null),
          citationSample: citations.length > 0 ? {
            firstCitation: citations[0],
            hasFilename: !!citations[0]?.filename,
            hasPageNumber: !!citations[0]?.pageNumber,
            hasChunkPreview: !!citations[0]?.chunkPreview,
            hasChunkText: !!citations[0]?.chunkText,
            hasChunkId: !!citations[0]?.chunkId,
            hasSimilarity: !!citations[0]?.similarity,
            hasSourceDocumentId: !!citations[0]?.sourceDocumentId
          } : null,
          dataProcessingResult: rawCitations.length > 0 ? {
            rawSample: rawCitations[0],
            processedSample: citations[0],
            conversionSuccessful: !!citations[0]?.filename
          } : null,
          fullMetadata: message.metadata
        });

        // Check for citation markers in content
        const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
        const citationMarkers = content.match(/\[\^(\d+)\]/g) || [];
        console.log("🎯 Citation markers in content:", {
          messageId: message.id,
          markersFound: citationMarkers.length,
          markers: citationMarkers,
          contentLength: content.length,
          contentSample: content.substring(0, 200) + '...'
        });

        // Test citation mapping
        if (citations.length > 0 && citationMarkers.length > 0) {
          console.log("🗺️ Citation mapping test:", {
            messageId: message.id,
            citationArrayLength: citations.length,
            markersInContent: citationMarkers.length,
            expectedMapping: citationMarkers.map((marker, index) => {
              const citationNumber = parseInt(marker.match(/\[\^(\d+)\]/)?.[1] || '0', 10);
              const citation = citations[citationNumber - 1]; // Array is 0-indexed, markers are 1-indexed
              return {
                marker,
                citationNumber,
                arrayIndex: citationNumber - 1,
                hasCitationData: !!citation,
                citationFilename: citation?.filename || 'N/A'
              };
            })
          });
        }
      }
    }
  }, [message.id, message.role, citationCount, citationEnabled, citationTriggered, citations, rawCitations, message.metadata?.inlineCitations]);

  // Auto-refresh message to pick up citations after streaming completes
  useEffect(() => {
    if (message.role === "assistant" && !citationCount && !isReadonly) {
      // Set a timer to refresh the message after 3 seconds to pick up any citations
      // that were processed after the streaming completed
      const timer = setTimeout(async () => {
        try {
          Logger.info("Auto-refreshing message to check for citations", { messageId: message.id });

          const response = await fetch(`/api/messages/${message.id}`, {
            headers: { "Cache-Control": "no-cache" }
          });

          if (response.ok) {
            const updatedMessage = await response.json();
            const updatedCitationCount = updatedMessage.metadata?.inlineCitations?.citationCount || 0;

            if (updatedCitationCount > 0 || updatedMessage.content !== message.content) {
              Logger.info("Citations found during auto-refresh, updating message", {
                messageId: message.id,
                citationCount: updatedCitationCount,
                contentChanged: updatedMessage.content !== message.content
              });

              // Update the message in the parent component
              setMessages((prevMessages) =>
                prevMessages.map((msg) =>
                  msg.id === message.id
                    ? {
                        ...msg,
                        content: updatedMessage.content,
                        metadata: {
                          ...msg.metadata,
                          ...updatedMessage.metadata
                        }
                      }
                    : msg
                )
              );
            }
          }
        } catch (error) {
          Logger.error("Error during auto-refresh for citations", { messageId: message.id, error });
        }
      }, 3000); // Wait 3 seconds after message appears

      return () => clearTimeout(timer);
    }
  }, [message.id, message.role, message.content, citationCount, isReadonly, setMessages]);

  const formatMessageTime = (timestamp: string | Date) => {
    if (!timestamp) return "";
    const date =
      typeof timestamp === "string" ? new Date(timestamp) : timestamp;

    // Always show full date and time format
    return format(date, "MMM d, yyyy, h:mm a");
  };

  useEffect(() => {
    const handleSelection = () => {
      // Showing 'copy' tooltip if user selects text
      const selection = window.getSelection();
      if (selection && selection.toString().length > 0) {
        // Check if selection is within our markdown component
        let isWithinMarkdown = false;
        if (markdownRef.current) {
          let node = selection.anchorNode;
          while (node) {
            if (node === markdownRef.current) {
              isWithinMarkdown = true;
              break;
            }
            node = node.parentNode;
          }
        }

        if (isWithinMarkdown) {
          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();
          setTooltipPosition({
            x: rect.left + rect.width / 2,
            y: rect.top - 10,
          });
          setShowCopyTooltip(true);
        }
      } else {
        setShowCopyTooltip(false);
      }
    };

    document.addEventListener("selectionchange", handleSelection);
    return () =>
      document.removeEventListener("selectionchange", handleSelection);
  }, []);

  const handleCopySelection = async () => {
    const selection = window.getSelection();

    if (selection && !selection.isCollapsed) {
      try {
        // Get the selected text
        const selectedText = selection.toString();

        // Get the HTML representation of the selection
        const range = selection.getRangeAt(0);
        const fragment = range.cloneContents();
        const tempDiv = document.createElement("div");
        tempDiv.appendChild(fragment);
        const selectedHtml = tempDiv.innerHTML;

        try {
          // Try to use the Clipboard API with both formats
          await navigator.clipboard.write([
            new ClipboardItem({
              "text/html": new Blob([selectedHtml], { type: "text/html" }),
              "text/plain": new Blob([selectedText], { type: "text/plain" }),
            }),
          ]);
        } catch (err) {
          // Fallback to the copyToClipboard function
          await copyToClipboard(selectedText);
        }

        // Show success message
        toast.success("Copied to clipboard!");

        // Hide the tooltip
        setShowCopyTooltip(false);
      } catch (error) {
        console.error("Error copying selection:", error);
        toast.error("Failed to copy text");
      }
    }
  };

  // Simplified check - just see if previous message is not user
  const isPreviousMessageNonUser = () => {
    // Only run on client side
    if (typeof document === "undefined") return false;

    const messages = document.querySelectorAll("[data-role]");
    const currentIndex = Array.from(messages).findIndex(
      (el) => el.getAttribute("data-message-id") === message.id
    );
    if (currentIndex <= 0) return false;

    const previousRole = messages[currentIndex - 1].getAttribute("data-role");
    return previousRole !== "user";
  };

  return (
    <AnimatePresence>
      <motion.div
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
        data-message-id={message.id}
        data-has-metadata={Boolean(message.metadata)}
        data-has-internet-results={Boolean(message.metadata?.internetResults)}
        data-has-error={isErrorMessage}
        data-has-relevant-images={Boolean(
          message.metadata?.relevantImages?.length
        )}
      >
        <div
          className={cn(
            "flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl",
            {
              "w-full": mode === "edit",
              "group-data-[role=user]/message:w-fit": mode !== "edit",
              "pb-4": message.role === "assistant",
            }
          )}
        >
          {message.role === "assistant" && !isPreviousMessageNonUser() && (
            <LogoIqidis size={50} />
          )}
          {message.role === "assistant" && isPreviousMessageNonUser() && (
            <div className="size-8" /> // Spacer to maintain alignment
          )}

          <div className={cn("flex flex-col gap-2 w-full")}>
            {message.experimental_attachments && (
              <div className="flex flex-row gap-2 overflow-x-auto pb-2 max-w-full scrollbar-thin scrollbar-thumb-zinc-400 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent">
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment
                    key={attachment.url}
                    attachment={attachment}
                  />
                ))}
              </div>
            )}

            {message.content && mode === "view" && (
              <div className="flex flex-row gap-2 items-start">
                {message.role === "user" && false && !isReadonly && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                        onClick={() => {
                          setMode("edit");
                        }}
                      >
                        <PencilEditIcon />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Edit message</TooltipContent>
                  </Tooltip>
                )}

                <div
                  className={cn("flex flex-col gap-4", {
                    "user-message-light dark:user-message-dark":
                      message.role === "user",
                    "assistant-message-light dark:assistant-message-dark shadow-md":
                      message.role === "assistant",
                  })}
                >
<<<<<<< HEAD
                  <div ref={markdownRef}>
                    <MarkdownWithInteractiveCitations
                      role={message.role}
                      citations={citations}
                    >
=======
                  <div
                    className="text-wrap word-break-auto-phrase"
                    ref={markdownRef}
                  >
                    <Markdown role={message.role}>
>>>>>>> development
                      {fixMessageContent(message.content)}
                    </MarkdownWithInteractiveCitations>

                    {message.createdAt && (
                      <>
                        <div className="border-t border-border/30 my-1 w-full"></div>
                        <div className="flex items-center justify-between">
                          <div
                            className="text-xs opacity-70 group-hover/message:opacity-100 transition-opacity
                            group-data-[role=user]/message:text-primary-foreground/80
                            group-data-[role=assistant]/message:text-muted-foreground
                            dark:group-data-[role=user]/message:text-gray-800"
                          >
                            {formatMessageTime(message.createdAt)}
                          </div>

                          {/* Citation badge (synchronous - no status indicator needed) */}
                          {message.role === "assistant" && citationCount > 0 && (
                            <div className="flex items-center gap-2">
                              <CitationBadge citationCount={citationCount} />
                            </div>
                          )}
                        </div>
                      </>
                    )}
                  </div>

                  {/* Add error message UI */}
                  {isErrorMessage && (
                    <ErrorMessage
                      message={message}
                      onRetry={onRetry}
                      chatId={chatId}
                    />
                  )}
                </div>
              </div>
            )}

            {message.role === "assistant" &&
              (message.metadata?.rewrittenQuery ||
                message.metadata?.chainOfThoughts) && (
                <React.Suspense fallback={null}>
                  <div className="flex items-center">
                    {(() => {
                      try {
                        return <RewrittenQueryDisplay message={message} />;
                      } catch (error) {
                        Logger.error(
                          "Error rendering RewrittenQueryDisplay:",
                          error
                        );
                        return null;
                      }
                    })()}

                    {/* Add the Citation Verification Display component */}
                    {message.role === "assistant" &&
                      (() => {
                        try {
                          return (
                            <CitationVerificationDisplay message={message} />
                          );
                        } catch (error) {
                          Logger.error(
                            "Error rendering CitationVerificationDisplay:",
                            error
                          );
                          return null;
                        }
                      })()}
                  </div>
                </React.Suspense>
              )}

            {message.content && mode === "edit" && (
              <div className="flex flex-row gap-2 items-start">
                <div className="size-8" />

                <MessageEditor
                  key={message.id}
                  message={message}
                  setMode={setMode}
                  setMessages={setMessages}
                  reload={reload}
                />
              </div>
            )}

            {message.toolInvocations && message.toolInvocations.length > 0 && (
              <div className="flex flex-col gap-4">
                {message.toolInvocations.map((toolInvocation) => {
                  const { toolName, toolCallId, state, args } = toolInvocation;

                  if (state === "result") {
                    const { result } = toolInvocation;

                    return (
                      <div key={toolCallId}>
                        {toolName === "createDocument" ? (
                          <DocumentPreview
                            isReadonly={isReadonly}
                            result={result}
                          />
                        ) : toolName === "updateDocument" ? (
                          <DocumentToolResult
                            type="update"
                            result={result}
                            isReadonly={isReadonly}
                          />
                        ) : toolName === "requestSuggestions" ? (
                          <DocumentToolResult
                            type="request-suggestions"
                            result={result}
                            isReadonly={isReadonly}
                          />
                        ) : (
                          <pre>{JSON.stringify(result, null, 2)}</pre>
                        )}
                      </div>
                    );
                  }
                  return (
                    <div
                      key={toolCallId}
                      className={cx({
                        skeleton: ["getWeather"].includes(toolName),
                      })}
                    >
                      {toolName === "createDocument" ? (
                        <DocumentPreview isReadonly={isReadonly} args={args} />
                      ) : toolName === "updateDocument" ? (
                        <DocumentToolCall
                          type="update"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : toolName === "requestSuggestions" ? (
                        <DocumentToolCall
                          type="request-suggestions"
                          args={args}
                          isReadonly={isReadonly}
                        />
                      ) : null}
                    </div>
                  );
                })}
              </div>
            )}

            {/* For assistant messages, put timestamp and actions outside the paper container */}
            {/* {message.role === "assistant" && message.createdAt && (
              <div className="text-xs mt-1.5 ml-1 opacity-70 group-hover/message:opacity-100 transition-opacity text-muted-foreground">
                {formatMessageTime(message.createdAt)}
              </div>
            )} */}

            {!isReadonly && (
              <MessageActions
                key={`action-${message.id}`}
                chatId={chatId}
                message={message}
                vote={vote}
                isLoading={isLoading}
                onRetry={onRetry}
              />
            )}
          </div>
        </div>
        {showCopyTooltip && (
          <div
            className="fixed z-50 bg-popover text-popover-foreground px-2 py-1 rounded shadow-md text-xs"
            style={{
              left: `${tooltipPosition.x}px`,
              top: `${tooltipPosition.y}px`,
              transform: "translate(-50%, -100%)",
            }}
          >
            <button
              className="flex items-center gap-1 hover:bg-muted p-1 rounded"
              onClick={handleCopySelection}
            >
              <CopyIcon size={12} />
              Copy
            </button>
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(
  PurePreviewMessage,
  (prevProps, nextProps) => {
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (prevProps.message.content !== nextProps.message.content) return false;
    if (
      !equal(
        prevProps.message.toolInvocations,
        nextProps.message.toolInvocations
      )
    )
      return false;
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    // Add metadata comparison
    if (!equal(prevProps.message.metadata, nextProps.message.metadata))
      return false;

    return true;
  }
);

export const ThinkingMessage = () => {
  const role = "assistant";

  return (
    <motion.div
      className="w-full mx-auto max-w-3xl px-4 group/message "
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          "flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",
          {
            "group-data-[role=user]/message:bg-muted": true,
          }
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border">
          <SparklingSparkleIcon size={14} />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">
            Thinking ...
          </div>
        </div>
      </div>
    </motion.div>
  );
};
