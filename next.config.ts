import type { NextConfig } from "next";
import { withSentryConfig } from "@sentry/nextjs";

const nextConfig: NextConfig = {
  /* config options here */
  experimental: {
    ppr: true,
    // Turbopack configuration for PDF.js worker prevention
    turbo: {
      resolveAlias: {
        // Alias PDF.js to legacy build for Turbopack
        'pdfjs-dist': 'pdfjs-dist/legacy/build/pdf.mjs',
      },
    },
  },
  serverExternalPackages: ["pdf-parse", "canvas", "pdfjs-dist"],
  webpack: (config, { isServer }) => {
    if (isServer) {
      // COMPREHENSIVE PDF.js worker prevention for server-side

      // 1. External worker files to prevent bundling
      config.externals = config.externals || [];
      config.externals.push({
        'pdfjs-dist/build/pdf.worker.mjs': 'commonjs pdfjs-dist/build/pdf.worker.mjs',
        'pdfjs-dist/legacy/build/pdf.worker.mjs': 'commonjs pdfjs-dist/legacy/build/pdf.worker.mjs',
      });

      // 2. Alias PDF.js to legacy build (only .mjs exists in 4.10.38)
      config.resolve.alias = {
        ...config.resolve.alias,
        'pdfjs-dist': 'pdfjs-dist/legacy/build/pdf.mjs',
      };

      // 3. Fallback worker files to false to prevent loading
      config.resolve.fallback = {
        ...config.resolve.fallback,
        'pdfjs-dist/build/pdf.worker.mjs': false,
        'pdfjs-dist/legacy/build/pdf.worker.mjs': false,
      };

      // 4. Additional plugin to completely ignore worker files
      config.plugins = config.plugins || [];
      config.plugins.push(
        new (require('webpack')).IgnorePlugin({
          resourceRegExp: /pdf\.worker\.mjs$/,
          contextRegExp: /pdfjs-dist/,
        })
      );
    }
    return config;
  },
  images: {
    remotePatterns: [
      {
        hostname: "avatar.vercel.sh",
      },
    ],
  },
};

// Make sure adding Sentry options is the last code to run before exporting
export default withSentryConfig(nextConfig, {
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,

  // Use auth token from environment variable
  authToken: process.env.SENTRY_AUTH_TOKEN,

  // Only print logs for uploading source maps in CI
  // Set to `true` to suppress logs
  silent: !process.env.CI,
  // Automatically tree-shake Sentry logger statements to reduce bundle size

  // Automatically annotate React components to show their full name in breadcrumbs and session replay
  reactComponentAnnotation: {
    enabled: true,
  },
  disableLogger: true,
  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
