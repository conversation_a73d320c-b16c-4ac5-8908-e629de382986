import { Logger } from "../utils/Logger";
import { OpenAIEmbeddingService } from "./openai-embedding";
import { db } from "../db";
import { resources, embeddings, sourceDocuments, messageDocuments } from "../db/schema";
import { eq, sql, and } from "drizzle-orm";
import { InlineCitationsPerformance, OPERATION_TYPES } from "./inline-citations-performance";
import { MarkdownStructureDetector } from "../utils/markdown-structure-detector";
import { CitationFormatter, DEFAULT_FORMATTING_OPTIONS, type FormattingOptions } from "../utils/citation-formatter";


// Configuration for inline citations generation
const CITATION_CONFIG = {
  SIMILARITY_THRESHOLD: 0.0,  // Primary similarity threshold
  MAX_CITATIONS: 20,
  CHUNK_PREVIEW_LENGTH: 150,
  FALLBACK_MAX_RESULTS: 20,   // Return top-20 results even if below threshold
} as const;

export interface CitationMatch {
  chunkId: string;
  similarity: number;
  chunkText: string;
  chunkPreview: string;
  pageNumber: number;
  sourceDocumentId: string;
  filename?: string;

  createdAt?: Date;
}

export interface CitationResult {
  success: boolean;
  originalContent: string;
  citedContent: string;
  citations: CitationMatch[];
  citationCount: number;
  insertionLocations: number[];
  error?: string;
}

export class InlineCitationsGenerator {
  /**
   * Main entry point for generating inline citations for a chat response
   * @param messageId The message ID
   * @param chatId The chat ID
   * @param content The assistant's response content
   * @returns Citation result with modified content and citation metadata
   */
  static async generateInlineCitations(
    messageId: string,
    chatId: string,
    content: string
  ): Promise<CitationResult> {
    try {
      Logger.info("Starting inline citations generation", {
        messageId,
        chatId,
        contentLength: content.length,
      });

      // Step 1: Get documents associated with this chat
      const associatedDocuments = await this.getAssociatedDocuments(chatId);

      if (associatedDocuments.length === 0) {
        Logger.info("No associated documents found for chat", { chatId });
        return {
          success: true,
          originalContent: content,
          citedContent: content,
          citations: [],
          citationCount: 0,
          insertionLocations: [],
        };
      }

      Logger.info("Found associated documents", {
        chatId,
        documentCount: associatedDocuments.length,
        documentIds: associatedDocuments.map(d => d.id),
      });

      // Step 2: Embed the response content
      const responseEmbedding = await this.embedResponse(content);

      // Step 3: Find similar chunks using simple similarity search
      const similarChunks = await this.findSimilarChunks(
        responseEmbedding,
        associatedDocuments.map(d => d.id),
        content
      );

      if (similarChunks.length === 0) {
        Logger.info("No similar chunks found above threshold", {
          messageId,
          threshold: CITATION_CONFIG.SIMILARITY_THRESHOLD,
        });
        return {
          success: true,
          originalContent: content,
          citedContent: content,
          citations: [],
          citationCount: 0,
          insertionLocations: [],
        };
      }

      // Step 4: Insert citation markers
      const citationResult = this.insertCitationMarkers(content, similarChunks);

      // Step 5: Log results
      this.logCitationResults(messageId, citationResult);

      return {
        success: true,
        originalContent: content,
        citedContent: citationResult.citedContent,
        citations: citationResult.citations,
        citationCount: citationResult.citations.length,
        insertionLocations: citationResult.insertionLocations,
      };
    } catch (error) {
      Logger.error("Error in inline citations generation", {
        messageId,
        chatId,
        error,
      });

      return {
        success: false,
        originalContent: content,
        citedContent: content,
        citations: [],
        citationCount: 0,
        insertionLocations: [],
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Gets documents associated with the chat through message-document relationships
   */
  private static async getAssociatedDocuments(chatId: string): Promise<Array<{ id: string; filename: string }>> {
    try {
      const documents = await db
        .select({
          id: sourceDocuments.id,
          filename: sourceDocuments.filename,
        })
        .from(sourceDocuments)
        .innerJoin(messageDocuments, eq(messageDocuments.sourceDocumentId, sourceDocuments.id))
        .innerJoin(
          sql`(SELECT DISTINCT "message_id" FROM "message_documents"
               WHERE "source_document_id" IN (
                 SELECT DISTINCT "source_document_id" FROM "message_documents"
                 INNER JOIN "Message" ON "Message"."id" = "message_documents"."message_id"
                 WHERE "Message"."chatId" = ${chatId}
               )) AS chat_messages`,
          sql`chat_messages.message_id = ${messageDocuments.messageId}`
        )
        .where(eq(sourceDocuments.chatId, chatId));

      return documents;
    } catch (error) {
      Logger.error("Error getting associated documents", { chatId, error });
      return [];
    }
  }

  /**
   * Embeds the response content using the same embedding logic as chunks
   */
  private static async embedResponse(content: string): Promise<number[]> {
    try {
      Logger.debug("Embedding response content", {
        contentLength: content.length,
      });

      const embedding = await OpenAIEmbeddingService.generateQueryEmbedding(content);

      Logger.debug("Response embedding generated", {
        embeddingLength: embedding.length,
      });

      return embedding;
    } catch (error) {
      Logger.error("Error embedding response content", { error });
      throw error;
    }
  }

  /**
   * Finds chunks using simple similarity search, returning top-20 results even if below threshold
   */
  private static async findSimilarChunks(
    responseEmbedding: number[],
    documentIds: string[],
    queryContent: string
  ): Promise<CitationMatch[]> {
    const operationId = `similarity_search_${Date.now()}`;
    const timer = InlineCitationsPerformance.startTimer(
      operationId,
      OPERATION_TYPES.SIMILARITY_SEARCH,
      {
        documentCount: documentIds.length,
        threshold: CITATION_CONFIG.SIMILARITY_THRESHOLD,
        embeddingDimensions: responseEmbedding.length,
      }
    );

    try {
      Logger.debug("Finding similar chunks", {
        documentCount: documentIds.length,
        threshold: CITATION_CONFIG.SIMILARITY_THRESHOLD,
      });

      // Verify query embedding normalization before similarity search
      const queryMagnitude = Math.sqrt(responseEmbedding.reduce((sum, val) => sum + val * val, 0));
      const isQueryNormalized = Math.abs(queryMagnitude - 1.0) < 0.001;

      if (!isQueryNormalized) {
        Logger.warn("Query embedding not normalized for similarity search", {
          magnitude: queryMagnitude.toFixed(6),
          expected: "~1.0"
        });
      }

      // Convert embedding to vector format for PostgreSQL
      const embeddingVector = `[${responseEmbedding.join(",")}]`;

      // First, try to get results above threshold
      const similarChunksAboveThreshold = await db
        .select({
          resourceId: resources.id,
          content: resources.content,
          sourceDocumentId: resources.sourceDocumentId,
          filename: sourceDocuments.filename,
          // Convert cosine distance to similarity: similarity = 1 - distance
          // This ensures we get proper cosine similarity values between -1 and 1
          similarity: sql<number>`1 - (${embeddings.embedding} <=> ${sql.raw(`'${embeddingVector}'::vector`)})`,
          createdAt: resources.createdAt,
        })
        .from(embeddings)
        .innerJoin(resources, eq(embeddings.resourceId, resources.id))
        .innerJoin(sourceDocuments, eq(sourceDocuments.id, resources.sourceDocumentId))
        .where(
          and(
            sql`${resources.sourceDocumentId} = ANY(${sql.raw(`'{${documentIds.join(",")}}'::uuid[]`)})`,
            // Use similarity threshold
            sql`${embeddings.embedding} <=> ${sql.raw(`'${embeddingVector}'::vector`)} < ${1 - CITATION_CONFIG.SIMILARITY_THRESHOLD}`
          )
        )
        // Order by similarity (descending)
        .orderBy(sql`${embeddings.embedding} <=> ${sql.raw(`'${embeddingVector}'::vector`)}`)
        .limit(CITATION_CONFIG.MAX_CITATIONS);

      let similarChunks = similarChunksAboveThreshold;

      // If no results above threshold, get top-20 results regardless of threshold
      if (similarChunks.length === 0) {
        Logger.debug("No results above threshold, fetching top results", {
          threshold: CITATION_CONFIG.SIMILARITY_THRESHOLD,
          fallbackLimit: CITATION_CONFIG.FALLBACK_MAX_RESULTS,
        });

        similarChunks = await db
          .select({
            resourceId: resources.id,
            content: resources.content,
            sourceDocumentId: resources.sourceDocumentId,
            filename: sourceDocuments.filename,
            similarity: sql<number>`1 - (${embeddings.embedding} <=> ${sql.raw(`'${embeddingVector}'::vector`)})`,
            createdAt: resources.createdAt,
          })
          .from(embeddings)
          .innerJoin(resources, eq(embeddings.resourceId, resources.id))
          .innerJoin(sourceDocuments, eq(sourceDocuments.id, resources.sourceDocumentId))
          .where(
            sql`${resources.sourceDocumentId} = ANY(${sql.raw(`'{${documentIds.join(",")}}'::uuid[]`)})`
          )
          // Order by similarity (descending)
          .orderBy(sql`${embeddings.embedding} <=> ${sql.raw(`'${embeddingVector}'::vector`)}`)
          .limit(CITATION_CONFIG.FALLBACK_MAX_RESULTS);
      }

      // Convert to CitationMatch format and sort by similarity
      const citationMatches: CitationMatch[] = similarChunks.map((chunk, index) => ({
        chunkId: chunk.resourceId,
        similarity: chunk.similarity,
        chunkText: chunk.content,
        chunkPreview: this.createChunkPreview(chunk.content),
        pageNumber: this.extractPageNumber(chunk.content, index, similarChunks.length),
        sourceDocumentId: chunk.sourceDocumentId,
        filename: chunk.filename,
        createdAt: chunk.createdAt,
      }));

      // Sort by similarity score (descending) - results are already ordered by DB query
      const sortedResults = citationMatches.sort((a, b) => b.similarity - a.similarity);

      const performanceResult = timer.end();

      Logger.debug("Similar chunks found with simple similarity search", {
        totalResults: sortedResults.length,
        threshold: CITATION_CONFIG.SIMILARITY_THRESHOLD,
        usedFallback: similarChunksAboveThreshold.length === 0,
        processingTime: `${performanceResult.duration.toFixed(2)}ms`,
        averageSimilarity: sortedResults.length > 0
          ? sortedResults.reduce((sum, c) => sum + c.similarity, 0) / sortedResults.length
          : 0,
        topResults: sortedResults.slice(0, 3).map(r => ({
          similarity: r.similarity.toFixed(3),
          filename: r.filename,
          preview: r.chunkPreview.substring(0, 50) + "...",
        })),
      });

      return sortedResults;
    } catch (error) {
      timer.end();
      Logger.error("Error finding similar chunks", { error });
      throw error;
    }
  }



  /**
   * Creates a preview of the chunk content
   */
  private static createChunkPreview(content: string): string {
    if (content.length <= CITATION_CONFIG.CHUNK_PREVIEW_LENGTH) {
      return content;
    }
    return content.substring(0, CITATION_CONFIG.CHUNK_PREVIEW_LENGTH) + "...";
  }

  /**
   * Extracts page number from chunk content or estimates based on chunk position
   */
  private static extractPageNumber(content: string, chunkIndex: number = 0, _totalChunks: number = 1): number {
    // Try to extract page number from content patterns
    // Look for patterns like "Page 5", "p. 5", "page 5", etc.
    const pagePatterns = [
      /(?:page|p\.?)\s*(\d+)/i,
      /\[page\s*(\d+)\]/i,
      /page:\s*(\d+)/i,
      /^(\d+)\s*$/m, // Standalone numbers that might be page numbers
    ];

    for (const pattern of pagePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        const pageNum = parseInt(match[1], 10);
        if (!isNaN(pageNum) && pageNum > 0 && pageNum < 1000) { // Reasonable page number range
          return pageNum;
        }
      }
    }

    // Estimate page number based on chunk position
    // Assume roughly 3-5 chunks per page
    const estimatedChunksPerPage = 4;
    const estimatedPage = Math.floor(chunkIndex / estimatedChunksPerPage) + 1;

    return estimatedPage;
  }

  /**
   * Inserts citation markers into the response content (without References section)
   * The References section is now handled by interactive tooltips in the UI
   */
  private static insertCitationMarkers(
    content: string,
    citations: CitationMatch[]
  ): {
    citedContent: string;
    citations: CitationMatch[];
    insertionLocations: number[];
  } {
    if (citations.length === 0) {
      return {
        citedContent: content,
        citations: [],
        insertionLocations: [],
      };
    }

    // Insert citation markers in the content
    const { contentWithCitations, insertionLocations } = this.insertSuperscriptCitations(content, citations);

    // Return content with citations (no References section needed for interactive tooltips)
    return {
      citedContent: contentWithCitations,
      citations: citations,
      insertionLocations,
    };
  }

  /**
   * Inserts superscript citation markers into the content using structure-aware placement
   */
  private static insertSuperscriptCitations(
    content: string,
    citations: CitationMatch[]
  ): {
    contentWithCitations: string;
    insertionLocations: number[];
  } {
    const insertionLocations: number[] = [];

    if (citations.length === 0) {
      return {
        contentWithCitations: content,
        insertionLocations,
      };
    }

    Logger.debug("Starting structure-aware citation insertion", {
      contentLength: content.length,
      citationCount: citations.length,
    });

    // Find safe insertion points using the new structure detector
    const safePoints = MarkdownStructureDetector.findSafeInsertionPoints(content);

    if (safePoints.length === 0) {
      Logger.warn("No safe insertion points found, falling back to end of content", {
        contentLength: content.length,
      });

      // Fallback: add all citations at the end of content
      let processedContent = content;
      citations.forEach((_, index) => {
        const citationMarker = `**${index + 1}** `;
        processedContent += citationMarker;
        insertionLocations.push(content.length + (index * citationMarker.length));
      });

      return {
        contentWithCitations: processedContent,
        insertionLocations,
      };
    }

    Logger.debug("Found safe insertion points", {
      safePointCount: safePoints.length,
      citationCount: citations.length,
    });

    // Distribute citations across safe points using file-type-aware logic
    const selectedPoints = this.selectOptimalInsertionPoints(safePoints, citations);

    // Insert citations from end to beginning to maintain position accuracy
    let processedContent = content;
    const sortedPoints = selectedPoints.sort((a, b) => b.position - a.position);

    sortedPoints.forEach((point) => {
      const citationMarker = `**${point.citationIndex + 1}** `;

      // Insert citation marker at the safe position
      processedContent =
        processedContent.substring(0, point.position) +
        citationMarker +
        processedContent.substring(point.position);

      insertionLocations.unshift(point.position);
    });

    Logger.debug("Citation insertion completed", {
      insertionCount: insertionLocations.length,
      finalContentLength: processedContent.length,
    });

    return {
      contentWithCitations: processedContent,
      insertionLocations,
    };
  }

  /**
   * Determines if a citation references a PDF or DOCX document
   */
  private static isPdfOrDocxCitation(citation: CitationMatch): boolean {
    if (!citation.filename) return false;
    const filename = citation.filename.toLowerCase();
    return filename.endsWith('.pdf') || filename.endsWith('.docx') || filename.endsWith('.doc');
  }

  /**
   * Selects optimal insertion points from available safe points
   * Now considers file types for different placement strategies
   */
  private static selectOptimalInsertionPoints(
    safePoints: Array<{ position: number; isAfterSentence: boolean; isEndOfParagraph: boolean; context: string }>,
    citations: CitationMatch[]
  ): Array<{ position: number; isAfterSentence: boolean; isEndOfParagraph: boolean; context: string; citationIndex: number }> {
    if (safePoints.length <= citations.length) {
      return safePoints.map((point, index) => ({ ...point, citationIndex: index }));
    }

    // Separate citations by file type
    const pdfDocxCitations: Array<{ citation: CitationMatch; index: number }> = [];
    const otherCitations: Array<{ citation: CitationMatch; index: number }> = [];

    citations.forEach((citation, index) => {
      if (this.isPdfOrDocxCitation(citation)) {
        pdfDocxCitations.push({ citation, index });
      } else {
        otherCitations.push({ citation, index });
      }
    });

    // Separate safe points by type
    const inlineSentencePoints = safePoints.filter(point => point.isAfterSentence && !point.isEndOfParagraph);
    const paragraphEndPoints = safePoints.filter(point => point.isEndOfParagraph);

    const selected: Array<{ position: number; isAfterSentence: boolean; isEndOfParagraph: boolean; context: string; citationIndex: number }> = [];

    // Place non-PDF/DOCX citations at inline sentence points (immediately after sentences)
    if (otherCitations.length > 0 && inlineSentencePoints.length > 0) {
      const step = Math.max(1, Math.floor(inlineSentencePoints.length / otherCitations.length));

      for (let i = 0; i < otherCitations.length; i++) {
        const pointIndex = Math.min(i * step, inlineSentencePoints.length - 1);
        selected.push({
          ...inlineSentencePoints[pointIndex],
          citationIndex: otherCitations[i].index
        });
      }
    }

    // Place PDF/DOCX citations at paragraph end points (with line breaks)
    if (pdfDocxCitations.length > 0 && paragraphEndPoints.length > 0) {
      const step = Math.max(1, Math.floor(paragraphEndPoints.length / pdfDocxCitations.length));

      for (let i = 0; i < pdfDocxCitations.length; i++) {
        const pointIndex = Math.min(i * step, paragraphEndPoints.length - 1);
        selected.push({
          ...paragraphEndPoints[pointIndex],
          citationIndex: pdfDocxCitations[i].index
        });
      }
    }

    // If we don't have enough appropriate points, fall back to distributing across all points
    const remainingCitations = citations.length - selected.length;
    if (remainingCitations > 0) {
      const usedPositions = new Set(selected.map(s => s.position));
      const availablePoints = safePoints.filter(point => !usedPositions.has(point.position));

      if (availablePoints.length > 0) {
        const step = Math.max(1, Math.floor(availablePoints.length / remainingCitations));
        let citationIndex = 0;

        // Find unassigned citation indices
        const assignedIndices = new Set(selected.map(s => s.citationIndex));
        const unassignedIndices = citations.map((_, index) => index).filter(index => !assignedIndices.has(index));

        for (let i = 0; i < Math.min(remainingCitations, availablePoints.length); i++) {
          const pointIndex = Math.min(i * step, availablePoints.length - 1);
          selected.push({
            ...availablePoints[pointIndex],
            citationIndex: unassignedIndices[i]
          });
        }
      }
    }

    return selected.sort((a, b) => a.position - b.position);
  }

  /**
   * Generates a References section with improved formatting and validation
   */
  private static generateReferencesSection(citations: CitationMatch[]): string {
    if (citations.length === 0) {
      return "";
    }

    Logger.debug("Generating references section", {
      citationCount: citations.length,
    });

    // Use the improved citation formatter with appropriate options
    const formattingOptions: FormattingOptions = {
      ...DEFAULT_FORMATTING_OPTIONS,
      // Adjust for better readability in chat interface
      maxFilenameLength: 50,
      maxPreviewLength: 120,
      compactMode: true, // Use compact mode for better mobile experience
    };

    const referencesSection = CitationFormatter.generateReferencesSection(citations, formattingOptions);

    // Validate the generated section
    const validation = CitationFormatter.validateReferencesSection(referencesSection);

    if (!validation.isValid) {
      Logger.warn("Reference section formatting issues detected", {
        issues: validation.issues,
        suggestions: validation.suggestions,
      });
    }

    Logger.debug("References section generated", {
      sectionLength: referencesSection.length,
      isValid: validation.isValid,
      issueCount: validation.issues.length,
    });

    return referencesSection;
  }

  /**
   * Logs comprehensive results of the citation generation process
   */
  private static logCitationResults(
    messageId: string,
    result: {
      citedContent: string;
      citations: CitationMatch[];
      insertionLocations: number[];
    }
  ): void {
    Logger.info("Inline citations generation completed", {
      messageId,
      citationCount: result.citations.length,
      insertionCount: result.insertionLocations.length,
      contentLengthChange: result.citedContent.length - result.citedContent.replace(/\[\^\d+\]/g, "").length,
      hasReferencesSection: result.citedContent.includes("## References"),
    });

    // Log citation mapping with similarity information
    const citationMapping = result.citations.map((citation, index) => ({
      citationNumber: index + 1,
      similarity: Math.round(citation.similarity * 100) / 100,
      filename: citation.filename,
      preview: citation.chunkPreview,
      pageNumber: citation.pageNumber,
    }));

    Logger.debug("Citation mapping details", {
      messageId,
      citationMapping,
      insertionLocations: result.insertionLocations,
    });
  }
}