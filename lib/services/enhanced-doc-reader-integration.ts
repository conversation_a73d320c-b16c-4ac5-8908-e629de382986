/**
 * Enhanced Doc Reader Image Integration Service
 * Provides image extraction using Enhanced Doc Reader's API Gateway and Fargate services
 * This service replaces ServerPdfOcrService for inline citations pipeline
 */

import { Logger } from "../utils/Logger";
import { DocumentPage } from "./gemini-ocr";
import { processPdfViaApiGateway } from "../utils/lambdav1";
import { processPdfViaFargate } from "../utils/fargate";

export interface EnhancedDocReaderOptions {
  preferredService?: 'api-gateway' | 'fargate';
  targetDpi?: number;
  optimizeSize?: boolean;
  includeDataUri?: boolean;
}

export interface EnhancedDocReaderResult {
  success: boolean;
  pages: DocumentPage[];
  metadata: {
    extractionMethod: string;
    serviceUsed: string;
    processingTime: number;
    totalPages: number;
    modeAgnostic: boolean;
    toggleStateIndependent: boolean;
    qualityValidated: boolean;
  };
  error?: string;
}

export class EnhancedDocReaderImageIntegration {
  /**
   * Processes a PDF from URL into images using Enhanced Doc Reader services
   * @param documentId The document ID for logging
   * @param url The PDF URL
   * @param options Optional processing options
   * @returns Array of document pages with base64 images
   */
  static async processPdfToImages(
    documentId: string,
    url: string,
    options?: EnhancedDocReaderOptions
  ): Promise<DocumentPage[]> {
    const startTime = Date.now();
    
    try {
      Logger.info("Starting Enhanced Doc Reader image extraction", {
        documentId,
        url,
        extractionMethod: 'enhanced-doc-reader',
        preferredService: options?.preferredService || 'api-gateway',
        fallbackEnabled: false,
      });

      const processingOptions = {
        preferredService: options?.preferredService || 'api-gateway',
        targetDpi: options?.targetDpi || 150,
        optimizeSize: options?.optimizeSize ?? true,
        includeDataUri: options?.includeDataUri ?? true,
      };

      let result;
      let serviceUsed: string;

      // Try API Gateway first (default Enhanced Doc Reader service)
      if (processingOptions.preferredService === 'api-gateway') {
        try {
          Logger.info("Using Enhanced Doc Reader API Gateway service", {
            documentId,
            extractionMethod: 'enhanced-doc-reader-api-gateway',
          });

          result = await processPdfViaApiGateway(documentId, url);
          serviceUsed = 'api-gateway';

          Logger.debug("Enhanced Doc Reader API Gateway response received", {
            documentId,
            resultType: typeof result,
            resultKeys: Object.keys(result || {}),
            hasStatusCode: 'statusCode' in (result || {}),
            hasBody: 'body' in (result || {}),
          });
        } catch (apiError) {
          Logger.error("Enhanced Doc Reader API Gateway failed", {
            documentId,
            error: apiError,
            extractionMethod: 'enhanced-doc-reader-api-gateway',
          });
          throw apiError; // No fallback - fail immediately
        }
      } else {
        // Use Fargate service
        try {
          Logger.info("Using Enhanced Doc Reader Fargate service", {
            documentId,
            extractionMethod: 'enhanced-doc-reader-fargate',
          });

          result = await processPdfViaFargate(documentId, url);
          serviceUsed = 'fargate';

          Logger.debug("Enhanced Doc Reader Fargate response received", {
            documentId,
            resultType: typeof result,
            resultKeys: Object.keys(result || {}),
            hasStatusCode: 'statusCode' in (result || {}),
            hasBody: 'body' in (result || {}),
          });
        } catch (fargateError) {
          Logger.error("Enhanced Doc Reader Fargate failed", {
            documentId,
            error: fargateError,
            extractionMethod: 'enhanced-doc-reader-fargate',
          });
          throw fargateError; // No fallback - fail immediately
        }
      }

      // Transform result to DocumentPage format
      const pages = this.transformToDocumentPages(result, documentId);

      if (!pages || pages.length === 0) {
        throw new Error("Enhanced Doc Reader returned no pages");
      }

      const processingTime = Date.now() - startTime;

      Logger.info("Enhanced Doc Reader image extraction completed successfully", {
        documentId,
        pagesProcessed: pages.length,
        processingTime,
        extractionMethod: 'enhanced-doc-reader',
        serviceUsed,
        totalImageSize: Math.round(pages.reduce((sum, page) => sum + page.imageBase64.length, 0) / 1024) + 'KB',
      });

      return pages;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      Logger.error("Enhanced Doc Reader image extraction failed", {
        documentId,
        error: errorMessage,
        processingTime,
        extractionMethod: 'enhanced-doc-reader',
        fallbackEnabled: false,
      });

      throw new Error(`Enhanced Doc Reader processing failed: ${errorMessage}`);
    }
  }

  /**
   * Transforms Enhanced Doc Reader API response to DocumentPage format
   * @param result The raw result from Enhanced Doc Reader services
   * @param documentId Document ID for logging
   * @returns Array of DocumentPage objects
   */
  private static transformToDocumentPages(result: any, documentId: string): DocumentPage[] {
    try {
      Logger.debug("Transforming Enhanced Doc Reader result to DocumentPage format", {
        documentId,
        resultKeys: Object.keys(result || {}),
      });

      // Handle HTTP response format (API Gateway returns statusCode + body)
      let data = result;
      if (result.statusCode && result.body) {
        Logger.debug("Detected HTTP response format, extracting body", {
          documentId,
          statusCode: result.statusCode,
        });

        // Parse the body if it's a string
        if (typeof result.body === 'string') {
          try {
            data = JSON.parse(result.body);
          } catch (parseError) {
            Logger.error("Failed to parse Enhanced Doc Reader response body", {
              documentId,
              body: result.body.substring(0, 200),
              parseError,
            });
            throw new Error("Invalid JSON in Enhanced Doc Reader response body");
          }
        } else {
          data = result.body;
        }
      }

      // Handle different response formats from API Gateway vs Fargate
      let pages: any[] = [];

      if (data.pages) {
        pages = data.pages;
      } else if (data.images) {
        pages = data.images;
      } else if (Array.isArray(data)) {
        pages = data;
      } else if (data.result && Array.isArray(data.result)) {
        pages = data.result;
      } else if (data.data && Array.isArray(data.data)) {
        pages = data.data;
      } else if (data.response && data.response.pages) {
        pages = data.response.pages;
      } else if (data.response && data.response.images) {
        pages = data.response.images;
      } else if (data.response && Array.isArray(data.response)) {
        pages = data.response;
      } else {
        Logger.error("Unexpected Enhanced Doc Reader response format", {
          documentId,
          dataKeys: Object.keys(data || {}),
          dataType: typeof data,
          hasPages: !!data.pages,
          hasImages: !!data.images,
          isArray: Array.isArray(data),
          hasResult: !!data.result,
          hasData: !!data.data,
          hasResponse: !!data.response,
          sampleData: JSON.stringify(data).substring(0, 500),
        });
        throw new Error("Unexpected Enhanced Doc Reader response format");
      }

      if (!Array.isArray(pages) || pages.length === 0) {
        Logger.warn("No pages found in Enhanced Doc Reader response", {
          documentId,
          pagesType: typeof pages,
          pagesLength: Array.isArray(pages) ? pages.length : 'not-array',
        });
        return [];
      }

      const documentPages: DocumentPage[] = pages.map((page, index) => {
        const pageNumber = page.page_number || page.pageNumber || (index + 1);
        let imageBase64 = page.image_base64 || page.imageBase64 || page.data_uri || page.image;

        // Ensure proper data URI format
        if (imageBase64 && !imageBase64.startsWith('data:')) {
          imageBase64 = `data:image/jpeg;base64,${imageBase64}`;
        }

        if (!imageBase64) {
          Logger.error("No image data found for page", {
            documentId,
            pageNumber,
            pageKeys: Object.keys(page || {}),
            hasImageBase64: !!page.image_base64,
            hasImageBase64Alt: !!page.imageBase64,
            hasDataUri: !!page.data_uri,
            hasImage: !!page.image,
          });
          throw new Error(`No image data found for page ${pageNumber}`);
        }

        return {
          pageNumber,
          imageBase64,
          width: page.width,
          height: page.height,
          metadata: {
            scaleFactor: 1.5, // Enhanced Doc Reader standard
            quality: 0.9, // Enhanced Doc Reader standard
            renderingApproach: 'enhanced-doc-reader',
            extractionMethod: 'enhanced-doc-reader',
            compatibilityMode: 'enhanced-doc-reader-native',
            modeAgnostic: true,
            toggleStateIndependent: true,
            qualityValidated: true,
          },
        };
      });

      Logger.info("Enhanced Doc Reader result transformation completed", {
        documentId,
        originalPageCount: pages.length,
        transformedPageCount: documentPages.length,
      });

      return documentPages;

    } catch (error) {
      Logger.error("Failed to transform Enhanced Doc Reader result", {
        documentId,
        error: error instanceof Error ? error.message : String(error),
        resultType: typeof result,
      });
      throw error;
    }
  }

  /**
   * Get detailed processing result with metadata
   * @param documentId The document ID
   * @param url The PDF URL
   * @param options Optional processing options
   * @returns Detailed processing result
   */
  static async processWithMetadata(
    documentId: string,
    url: string,
    options?: EnhancedDocReaderOptions
  ): Promise<EnhancedDocReaderResult> {
    const startTime = Date.now();

    try {
      const pages = await this.processPdfToImages(documentId, url, options);
      const processingTime = Date.now() - startTime;

      return {
        success: true,
        pages,
        metadata: {
          extractionMethod: 'enhanced-doc-reader',
          serviceUsed: options?.preferredService || 'api-gateway',
          processingTime,
          totalPages: pages.length,
          modeAgnostic: true,
          toggleStateIndependent: true,
          qualityValidated: true,
        },
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        success: false,
        pages: [],
        metadata: {
          extractionMethod: 'enhanced-doc-reader',
          serviceUsed: options?.preferredService || 'api-gateway',
          processingTime,
          totalPages: 0,
          modeAgnostic: true,
          toggleStateIndependent: true,
          qualityValidated: false,
        },
        error: errorMessage,
      };
    }
  }
}
