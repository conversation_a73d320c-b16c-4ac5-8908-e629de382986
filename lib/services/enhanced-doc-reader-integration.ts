/**
 * Enhanced Doc Reader Image Integration Service
 * Provides image extraction using Enhanced Doc Reader's API Gateway and Fargate services
 * This service replaces ServerPdfOcrService for inline citations pipeline
 */

import { Logger } from "../utils/Logger";
import { DocumentPage } from "./gemini-ocr";
import { processPdfViaApiGateway } from "../utils/lambdav1";
import { processPdfViaFargate } from "../utils/fargate";
import { getSignedUrlToDownload } from "../utils/s3Object";
import { db } from "../db/db";
import { resources, sourceDocuments } from "../db/schema";
import { eq, and } from "drizzle-orm";

export interface EnhancedDocReaderOptions {
  preferredService?: 'api-gateway' | 'fargate';
  targetDpi?: number;
  optimizeSize?: boolean;
  includeDataUri?: boolean;
}

export interface EnhancedDocReaderResult {
  success: boolean;
  pages: DocumentPage[];
  metadata: {
    extractionMethod: string;
    serviceUsed: string;
    processingTime: number;
    totalPages: number;
    modeAgnostic: boolean;
    toggleStateIndependent: boolean;
    qualityValidated: boolean;
  };
  error?: string;
}

export class EnhancedDocReaderImageIntegration {
  /**
   * Processes a PDF from URL into images using Enhanced Doc Reader services
   * @param documentId The document ID for logging
   * @param url The PDF URL
   * @param options Optional processing options
   * @returns Array of document pages with base64 images
   */
  static async processPdfToImages(
    documentId: string,
    url: string,
    options?: EnhancedDocReaderOptions
  ): Promise<DocumentPage[]> {
    const startTime = Date.now();

    try {
      Logger.info("Starting Enhanced Doc Reader image extraction", {
        documentId,
        url,
        extractionMethod: 'enhanced-doc-reader',
        preferredService: options?.preferredService || 'api-gateway',
        fallbackEnabled: false,
      });

      // Generate signed URL if this is an S3 URL
      const processedUrl = await this.prepareUrlForProcessing(url, documentId);

      const processingOptions = {
        preferredService: options?.preferredService || 'api-gateway',
        targetDpi: options?.targetDpi || 150,
        optimizeSize: options?.optimizeSize ?? true,
        includeDataUri: options?.includeDataUri ?? true,
      };

      let result;
      let serviceUsed: string;

      // Try API Gateway first (default Enhanced Doc Reader service)
      if (processingOptions.preferredService === 'api-gateway') {
        try {
          Logger.info("Using Enhanced Doc Reader API Gateway service", {
            documentId,
            extractionMethod: 'enhanced-doc-reader-api-gateway',
          });

          result = await processPdfViaApiGateway(documentId, processedUrl);
          serviceUsed = 'api-gateway';

          Logger.debug("Enhanced Doc Reader API Gateway response received", {
            documentId,
            resultType: typeof result,
            resultKeys: Object.keys(result || {}),
            hasStatusCode: 'statusCode' in (result || {}),
            hasBody: 'body' in (result || {}),
          });
        } catch (apiError) {
          Logger.error("Enhanced Doc Reader API Gateway failed", {
            documentId,
            error: apiError,
            extractionMethod: 'enhanced-doc-reader-api-gateway',
          });
          throw apiError; // No fallback - fail immediately
        }
      } else {
        // Use Fargate service
        try {
          Logger.info("Using Enhanced Doc Reader Fargate service", {
            documentId,
            extractionMethod: 'enhanced-doc-reader-fargate',
          });

          result = await processPdfViaFargate(documentId, processedUrl);
          serviceUsed = 'fargate';

          Logger.debug("Enhanced Doc Reader Fargate response received", {
            documentId,
            resultType: typeof result,
            resultKeys: Object.keys(result || {}),
            hasStatusCode: 'statusCode' in (result || {}),
            hasBody: 'body' in (result || {}),
          });
        } catch (fargateError) {
          Logger.error("Enhanced Doc Reader Fargate failed", {
            documentId,
            error: fargateError,
            extractionMethod: 'enhanced-doc-reader-fargate',
          });
          throw fargateError; // No fallback - fail immediately
        }
      }

      // Transform result to DocumentPage format
      const pages = this.transformToDocumentPages(result, documentId);

      if (!pages || pages.length === 0) {
        throw new Error("Enhanced Doc Reader returned no pages");
      }

      const processingTime = Date.now() - startTime;

      Logger.info("Enhanced Doc Reader image extraction completed successfully", {
        documentId,
        pagesProcessed: pages.length,
        processingTime,
        extractionMethod: 'enhanced-doc-reader',
        serviceUsed,
        totalImageSize: Math.round(pages.reduce((sum, page) => sum + page.imageBase64.length, 0) / 1024) + 'KB',
      });

      return pages;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      Logger.error("Enhanced Doc Reader image extraction failed", {
        documentId,
        error: errorMessage,
        processingTime,
        extractionMethod: 'enhanced-doc-reader',
        fallbackEnabled: false,
      });

      throw new Error(`Enhanced Doc Reader processing failed: ${errorMessage}`);
    }
  }

  /**
   * Transforms Enhanced Doc Reader API response to DocumentPage format
   * @param result The raw result from Enhanced Doc Reader services
   * @param documentId Document ID for logging
   * @returns Array of DocumentPage objects
   */
  private static transformToDocumentPages(result: any, documentId: string): DocumentPage[] {
    try {
      Logger.debug("Transforming Enhanced Doc Reader result to DocumentPage format", {
        documentId,
        resultKeys: Object.keys(result || {}),
      });

      // Handle HTTP response format (API Gateway returns statusCode + body)
      let data = result;
      if (result.statusCode && result.body) {
        Logger.debug("Detected HTTP response format, extracting body", {
          documentId,
          statusCode: result.statusCode,
        });

        // Check for error status codes
        if (result.statusCode >= 400) {
          Logger.error("Enhanced Doc Reader service returned error status", {
            documentId,
            statusCode: result.statusCode,
            body: result.body,
          });

          // Try to parse error message from body
          let errorMessage = `Enhanced Doc Reader service error (${result.statusCode})`;
          if (typeof result.body === 'string') {
            try {
              const errorData = JSON.parse(result.body);
              if (errorData.error) {
                errorMessage += `: ${errorData.error}`;
              }
            } catch {
              errorMessage += `: ${result.body}`;
            }
          }

          throw new Error(errorMessage);
        }

        // Parse the body if it's a string
        if (typeof result.body === 'string') {
          try {
            data = JSON.parse(result.body);
          } catch (parseError) {
            Logger.error("Failed to parse Enhanced Doc Reader response body", {
              documentId,
              body: result.body.substring(0, 200),
              parseError,
            });
            throw new Error("Invalid JSON in Enhanced Doc Reader response body");
          }
        } else {
          data = result.body;
        }
      }

      // Check for async processing response (success message without actual image data)
      if (data.success && data.message && data.pages_count && !data.pages && !data.images) {
        Logger.warn("Enhanced Doc Reader returned async processing response", {
          documentId,
          message: data.message,
          pagesCount: data.pages_count,
          responseType: 'async-processing-started',
        });
        throw new Error(`Enhanced Doc Reader async processing detected: ${data.message}. This indicates the service is processing asynchronously but the pipeline expects synchronous results.`);
      }

      // Handle Enhanced Doc Reader asynchronous response format
      if (data.success && data.pages_count && data.message) {
        Logger.info("Enhanced Doc Reader processed document asynchronously", {
          documentId,
          pagesCount: data.pages_count,
          message: data.message,
          serviceUsed,
        });

        // Enhanced Doc Reader processed the document but stored results in database
        // We need to retrieve the stored images from the database
        Logger.info("Retrieving processed images from database", {
          documentId,
          expectedPages: data.pages_count,
        });

        const storedPages = await this.retrieveStoredImages(documentId, data.pages_count);

        if (storedPages.length > 0) {
          Logger.info("Successfully retrieved stored images from Enhanced Doc Reader processing", {
            documentId,
            retrievedPages: storedPages.length,
            expectedPages: data.pages_count,
          });
          return storedPages;
        } else {
          Logger.warn("No stored images found, Enhanced Doc Reader may still be processing", {
            documentId,
            expectedPages: data.pages_count,
          });

          // Return empty array to indicate processing is not complete
          // The caller should handle this case appropriately
          return [];
        }
      }

      // Handle direct image response formats (for backward compatibility)
      let pages: any[] = [];

      if (data.pages) {
        pages = data.pages;
      } else if (data.images) {
        pages = data.images;
      } else if (Array.isArray(data)) {
        pages = data;
      } else if (data.result && Array.isArray(data.result)) {
        pages = data.result;
      } else if (data.data && Array.isArray(data.data)) {
        pages = data.data;
      } else if (data.response && data.response.pages) {
        pages = data.response.pages;
      } else if (data.response && data.response.images) {
        pages = data.response.images;
      } else if (data.response && Array.isArray(data.response)) {
        pages = data.response;
      } else {
        Logger.error("Unexpected Enhanced Doc Reader response format", {
          documentId,
          dataKeys: Object.keys(data || {}),
          dataType: typeof data,
          hasPages: !!data.pages,
          hasImages: !!data.images,
          isArray: Array.isArray(data),
          hasResult: !!data.result,
          hasData: !!data.data,
          hasResponse: !!data.response,
          hasSuccess: !!data.success,
          hasPagesCount: !!data.pages_count,
          hasMessage: !!data.message,
          sampleData: JSON.stringify(data).substring(0, 500),
        });
        throw new Error("Unexpected Enhanced Doc Reader response format");
      }

      if (!Array.isArray(pages) || pages.length === 0) {
        Logger.warn("No pages found in Enhanced Doc Reader response", {
          documentId,
          pagesType: typeof pages,
          pagesLength: Array.isArray(pages) ? pages.length : 'not-array',
        });
        return [];
      }

      const documentPages: DocumentPage[] = pages.map((page, index) => {
        const pageNumber = page.page_number || page.pageNumber || (index + 1);
        let imageBase64 = page.image_base64 || page.imageBase64 || page.data_uri || page.image;

        // Ensure proper data URI format
        if (imageBase64 && !imageBase64.startsWith('data:')) {
          imageBase64 = `data:image/jpeg;base64,${imageBase64}`;
        }

        if (!imageBase64) {
          Logger.error("No image data found for page", {
            documentId,
            pageNumber,
            pageKeys: Object.keys(page || {}),
            hasImageBase64: !!page.image_base64,
            hasImageBase64Alt: !!page.imageBase64,
            hasDataUri: !!page.data_uri,
            hasImage: !!page.image,
          });
          throw new Error(`No image data found for page ${pageNumber}`);
        }

        return {
          pageNumber,
          imageBase64,
          width: page.width,
          height: page.height,
          metadata: {
            scaleFactor: 1.5, // Enhanced Doc Reader standard
            quality: 0.9, // Enhanced Doc Reader standard
            renderingApproach: 'enhanced-doc-reader',
            extractionMethod: 'enhanced-doc-reader',
            compatibilityMode: 'enhanced-doc-reader-native',
            modeAgnostic: true,
            toggleStateIndependent: true,
            qualityValidated: true,
          },
        };
      });

      Logger.info("Enhanced Doc Reader result transformation completed", {
        documentId,
        originalPageCount: pages.length,
        transformedPageCount: documentPages.length,
      });

      return documentPages;

    } catch (error) {
      Logger.error("Failed to transform Enhanced Doc Reader result", {
        documentId,
        error: error instanceof Error ? error.message : String(error),
        resultType: typeof result,
      });
      throw error;
    }
  }

  /**
   * Get detailed processing result with metadata
   * @param documentId The document ID
   * @param url The PDF URL
   * @param options Optional processing options
   * @returns Detailed processing result
   */
  static async processWithMetadata(
    documentId: string,
    url: string,
    options?: EnhancedDocReaderOptions
  ): Promise<EnhancedDocReaderResult> {
    const startTime = Date.now();

    try {
      const pages = await this.processPdfToImages(documentId, url, options);
      const processingTime = Date.now() - startTime;

      return {
        success: true,
        pages,
        metadata: {
          extractionMethod: 'enhanced-doc-reader',
          serviceUsed: options?.preferredService || 'api-gateway',
          processingTime,
          totalPages: pages.length,
          modeAgnostic: true,
          toggleStateIndependent: true,
          qualityValidated: true,
        },
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      return {
        success: false,
        pages: [],
        metadata: {
          extractionMethod: 'enhanced-doc-reader',
          serviceUsed: options?.preferredService || 'api-gateway',
          processingTime,
          totalPages: 0,
          modeAgnostic: true,
          toggleStateIndependent: true,
          qualityValidated: false,
        },
        error: errorMessage,
      };
    }
  }

  /**
   * Prepares URL for processing by Enhanced Doc Reader services
   * Generates signed URLs for S3 URLs to ensure proper access
   * @param url The original URL
   * @param documentId Document ID for logging
   * @returns Processed URL (signed URL for S3, original URL for others)
   */
  private static async prepareUrlForProcessing(url: string, documentId: string): Promise<string> {
    try {
      // Check if this is an S3 URL
      if (this.isS3Url(url)) {
        Logger.info("Detected S3 URL, generating signed URL for Enhanced Doc Reader access", {
          documentId,
          originalUrl: url,
        });

        const { bucket, key } = this.parseS3Url(url);

        // Generate signed URL with extended expiration for Enhanced Doc Reader processing
        const signedUrl = await getSignedUrlToDownload({
          key,
          bucket,
          mimeType: 'application/pdf',
          otherOptions: {
            ResponseContentDisposition: 'inline',
          }
        }, { expiresIn: 3600 }); // 1 hour expiration

        Logger.info("Generated signed URL for Enhanced Doc Reader", {
          documentId,
          bucket,
          key,
          signedUrlGenerated: true,
        });

        return signedUrl;
      } else {
        Logger.debug("Non-S3 URL detected, using original URL", {
          documentId,
          url,
        });
        return url;
      }
    } catch (error) {
      Logger.error("Failed to prepare URL for Enhanced Doc Reader processing", {
        documentId,
        url,
        error: error instanceof Error ? error.message : String(error),
      });

      // Return original URL as fallback
      return url;
    }
  }

  /**
   * Checks if a URL is an S3 URL
   * @param url The URL to check
   * @returns True if it's an S3 URL
   */
  private static isS3Url(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.includes('s3') || urlObj.hostname.includes('amazonaws.com');
    } catch {
      return false;
    }
  }

  /**
   * Parses S3 URL to extract bucket and key
   * @param url The S3 URL
   * @returns Object with bucket and key
   */
  private static parseS3Url(url: string): { bucket: string; key: string } {
    const urlObj = new URL(url);

    // Handle different S3 URL formats
    if (urlObj.hostname.includes('.s3.') || urlObj.hostname.includes('.s3-')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      const bucket = urlObj.hostname.split('.')[0];
      const key = urlObj.pathname.substring(1); // Remove leading slash
      return { bucket, key };
    } else if (urlObj.hostname === 's3.amazonaws.com') {
      // Format: https://s3.amazonaws.com/bucket/key
      const pathParts = urlObj.pathname.substring(1).split('/');
      const bucket = pathParts[0];
      const key = pathParts.slice(1).join('/');
      return { bucket, key };
    } else {
      // Fallback: assume bucket is first part of hostname
      const bucket = urlObj.hostname.split('.')[0];
      const key = urlObj.pathname.substring(1);
      return { bucket, key };
    }
  }

  /**
   * Retrieves stored images from the database after Enhanced Doc Reader processing
   * @param documentId The document ID
   * @param expectedPages Expected number of pages
   * @returns Array of DocumentPage objects from stored data
   */
  private static async retrieveStoredImages(documentId: string, expectedPages: number): Promise<DocumentPage[]> {
    try {
      Logger.info("Retrieving stored images from Enhanced Doc Reader processing", {
        documentId,
        expectedPages,
      });

      // Query the resources table for stored images
      const storedResources = await db
        .select({
          content: resources.content,
          id: resources.id,
        })
        .from(resources)
        .innerJoin(sourceDocuments, eq(resources.sourceDocumentId, sourceDocuments.id))
        .where(
          and(
            eq(sourceDocuments.id, documentId),
            // Filter for image content (base64 data URIs)
            // Resources with image content will have data:image prefix
          )
        )
        .orderBy(resources.createdAt);

      Logger.debug("Retrieved stored resources from database", {
        documentId,
        resourceCount: storedResources.length,
        expectedPages,
      });

      // Filter and transform stored resources to DocumentPage format
      const documentPages: DocumentPage[] = [];
      let pageNumber = 1;

      for (const resource of storedResources) {
        // Check if this resource contains image data
        if (resource.content && resource.content.startsWith('data:image/')) {
          documentPages.push({
            pageNumber,
            imageBase64: resource.content,
            width: undefined, // Not stored in resources table
            height: undefined, // Not stored in resources table
            metadata: {
              scaleFactor: 1.5,
              quality: 0.9,
              renderingApproach: 'enhanced-doc-reader',
              extractionMethod: 'enhanced-doc-reader-stored',
              compatibilityMode: 'enhanced-doc-reader-database',
              modeAgnostic: true,
              toggleStateIndependent: true,
              qualityValidated: true,
            },
          });
          pageNumber++;
        }
      }

      Logger.info("Successfully retrieved and transformed stored images", {
        documentId,
        retrievedPages: documentPages.length,
        expectedPages,
        allPagesRetrieved: documentPages.length === expectedPages,
      });

      return documentPages;

    } catch (error) {
      Logger.error("Failed to retrieve stored images from database", {
        documentId,
        expectedPages,
        error: error instanceof Error ? error.message : String(error),
      });
      return [];
    }
  }

  /**
   * Checks if Enhanced Doc Reader processing is complete for a document
   * @param documentId The document ID
   * @returns Object with completion status and available page count
   */
  static async checkProcessingStatus(documentId: string): Promise<{
    isComplete: boolean;
    availablePages: number;
    hasStoredImages: boolean;
  }> {
    try {
      Logger.debug("Checking Enhanced Doc Reader processing status", {
        documentId,
      });

      // Query for stored image resources
      const storedResources = await db
        .select({
          content: resources.content,
        })
        .from(resources)
        .innerJoin(sourceDocuments, eq(resources.sourceDocumentId, sourceDocuments.id))
        .where(eq(sourceDocuments.id, documentId));

      // Count resources that contain image data
      const imageResources = storedResources.filter(resource =>
        resource.content && resource.content.startsWith('data:image/')
      );

      const hasStoredImages = imageResources.length > 0;
      const availablePages = imageResources.length;

      Logger.debug("Enhanced Doc Reader processing status checked", {
        documentId,
        hasStoredImages,
        availablePages,
        totalResources: storedResources.length,
      });

      return {
        isComplete: hasStoredImages,
        availablePages,
        hasStoredImages,
      };

    } catch (error) {
      Logger.error("Failed to check Enhanced Doc Reader processing status", {
        documentId,
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        isComplete: false,
        availablePages: 0,
        hasStoredImages: false,
      };
    }
  }
}
