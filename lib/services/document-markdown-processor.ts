import { Logger } from "../utils/Logger";
import { GeminiOCRService, DocumentPage } from "./gemini-ocr";
import { db } from "../db/db";
import { sourceDocuments, resources } from "../db/schema";
import { eq, sql, and } from "drizzle-orm";
import { InlineCitationsChunker } from "./inline-citations-chunker";
import { OpenAIEmbeddingService } from "./openai-embedding";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import { EnhancedDocReaderImageIntegration } from "./enhanced-doc-reader-integration";



// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

// Interface for processing results
export interface ProcessingResult {
  success: boolean;
  documentId: string;
  markdown?: string;
  pagesProcessed: number;
  error?: string;
}

/**
 * Downloads a file from S3 or Vercel Blob and returns it as a Buffer
 * @param url The file URL
 * @returns Buffer containing the file data
 */
async function downloadFileToBuffer(url: string): Promise<Buffer> {
  // Check if this is a Vercel Blob URL
  if (
    url.includes("vercel-storage.com") ||
    url.includes("blob.vercel-storage.com")
  ) {
    // Handle Vercel Blob URL with a direct fetch
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch from Vercel Blob: ${response.status}`);
    }
    return Buffer.from(await response.arrayBuffer());
  }

  // Handle S3 URLs with proper authentication
  const urlObj = new URL(url);
  const bucketName = urlObj.hostname.split(".")[0];
  const key = urlObj.pathname.substring(1); // Remove leading slash

  // Fetching object directly from S3
  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  });

  const s3Response = await s3Client.send(command);

  // Convert stream to buffer
  const chunks = [];
  for await (const chunk of s3Response.Body as Readable) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}

export class DocumentMarkdownProcessor {
  /**
   * Main entry point for processing documents into markdown
   * @param documentId The document ID
   * @param url The document URL
   * @param contentType The document content type
   * @returns Processing result
   */
  static async processDocument(
    documentId: string,
    url: string,
    contentType: string
  ): Promise<ProcessingResult> {
    try {
      Logger.info("Starting document markdown processing", {
        documentId,
        url,
        contentType,
      });

      // Check if document is a PDF
      if (contentType === "application/pdf" || url.toLowerCase().endsWith(".pdf")) {
        // Use Enhanced Doc Reader OCR approach (same image extraction as Enhanced Doc Reader + Gemini OCR)
        return await this.processPDFDocumentWithOCR(documentId, url);
      }

      // Check if document is an image
      if (contentType.startsWith("image/")) {
        return await this.processImageDocument(documentId, url, contentType);
      }

      Logger.warn("Unsupported document type for markdown processing", {
        documentId,
        contentType,
      });

      return {
        success: false,
        documentId,
        pagesProcessed: 0,
        error: `Unsupported document type: ${contentType}`,
      };
    } catch (error) {
      Logger.error("Error in document markdown processing", {
        documentId,
        error,
      });

      return {
        success: false,
        documentId,
        pagesProcessed: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }



  /**
   * Processes a PDF document using Enhanced Doc Reader OCR approach for inline citations
   * Uses the exact same image extraction mechanism as Enhanced Doc Reader
   * @param documentId The document ID
   * @param url The PDF URL
   * @returns Processing result
   */
  private static async processPDFDocumentWithOCR(
    documentId: string,
    url: string
  ): Promise<ProcessingResult> {
    try {
      Logger.info("Processing PDF document using Enhanced Doc Reader for inline citations", {
        documentId,
        url,
        extractionMethod: 'enhanced-doc-reader',
        targetPipeline: 'inline-citations',
      });

      // Process PDF pages to images using Enhanced Doc Reader
      Logger.info("Using Enhanced Doc Reader for inline citations", {
        documentId,
        extractionMethod: 'enhanced-doc-reader',
        targetPipeline: 'inline-citations',
        fallbackEnabled: false,
      });

      const pages = await EnhancedDocReaderImageIntegration.processPdfToImages(documentId, url);

      if (!pages || pages.length === 0) {
        throw new Error("Enhanced Doc Reader processing failed - no pages extracted");
      }

      if (pages.length === 0) {
        throw new Error("No pages could be processed from PDF using Enhanced Doc Reader method");
      }

      Logger.info(`Enhanced Doc Reader pages extracted successfully for inline citations. Processing ${pages.length} pages with Gemini OCR`, {
        documentId,
        pageCount: pages.length,
        extractionMethod: 'enhanced-doc-reader',
        totalImageSize: Math.round(pages.reduce((sum, page) => sum + page.imageBase64.length, 0) / 1024) + 'KB',
      });

      // Step 1: Store extracted images for "Cites to your documents" functionality
      Logger.info("Storing extracted images for reuse in Cites to your documents", {
        documentId,
        pageCount: pages.length,
        optimizedFlow: 'extract-once-store-reuse',
      });

      const extractionMetadata = {
        extractionMethod: 'enhanced-doc-reader',
        modeAgnostic: true,
        toggleStateIndependent: true,
        qualityValidated: true,
      };

      await this.storeExtractedImages(documentId, pages, extractionMetadata);

      // Step 2: Process with Gemini OCR using the same extracted images
      Logger.info("Processing extracted images with Gemini OCR", {
        documentId,
        pageCount: pages.length,
        reusingExtractedImages: true,
      });

      const ocrResult = await GeminiOCRService.processDocumentPages(pages, documentId);

      if (ocrResult.success && ocrResult.markdown) {
        // Store markdown in database
        await this.storeMarkdownInDatabase(documentId, ocrResult.markdown);

        Logger.info("Server PDF OCR processing completed successfully for inline citations", {
          documentId,
          pagesProcessed: ocrResult.pagesProcessed,
          markdownLength: ocrResult.markdown.length,
          extractionMethod: 'server-pdf-ocr-service',
        });

        return {
          success: true,
          documentId,
          markdown: ocrResult.markdown,
          pagesProcessed: ocrResult.pagesProcessed,
        };
      } else {
        throw new Error(ocrResult.error || "Server PDF OCR processing failed for inline citations");
      }
    } catch (error) {
      Logger.error("Error in server PDF OCR processing for inline citations", {
        documentId,
        error,
        extractionMethod: 'server-pdf-ocr-service',
      });

      // Return error result instead of throwing to prevent fallback
      return {
        success: false,
        documentId,
        pagesProcessed: 0,
        error: error instanceof Error ? error.message : "Server PDF OCR processing failed for inline citations",
      };
    }
  }









  /**
   * Processes a single image document into markdown
   * @param documentId The document ID
   * @param url The image URL
   * @param contentType The image content type
   * @returns Processing result
   */
  private static async processImageDocument(
    documentId: string,
    url: string,
    contentType: string
  ): Promise<ProcessingResult> {
    try {
      Logger.info("Processing image document", { documentId, url, contentType });

      // Download image using proper S3 authentication and convert to base64
      const buffer = await downloadFileToBuffer(url);
      const base64 = buffer.toString('base64');
      const imageBase64 = `data:${contentType};base64,${base64}`;

      const pages: DocumentPage[] = [{
        pageNumber: 1,
        imageBase64,
      }];

      // Process with Gemini OCR
      Logger.info("DEBUG: Starting Gemini OCR processing", {
        documentId,
        pageCount: pages.length,
      });

      const ocrResult = await GeminiOCRService.processDocumentPages(pages, documentId);

      Logger.info("DEBUG: Gemini OCR processing completed", {
        documentId,
        success: ocrResult.success,
        markdownLength: ocrResult.markdown?.length || 0,
        pagesProcessed: ocrResult.pagesProcessed,
        error: ocrResult.error,
      });

      if (ocrResult.success && ocrResult.markdown) {
        // DEBUG: Log markdown preview before storing
        const markdownPreview = ocrResult.markdown.substring(0, 500);
        Logger.info("DEBUG: Markdown preview before storage", {
          documentId,
          markdownPreview,
          fullLength: ocrResult.markdown.length,
        });

        // Store markdown in database
        await this.storeMarkdownInDatabase(documentId, ocrResult.markdown);

        Logger.info("Image markdown processing completed successfully", {
          documentId,
          markdownLength: ocrResult.markdown.length,
        });

        return {
          success: true,
          documentId,
          markdown: ocrResult.markdown,
          pagesProcessed: 1,
        };
      } else {
        Logger.error("DEBUG: OCR processing failed", {
          documentId,
          error: ocrResult.error,
          markdownLength: ocrResult.markdown?.length || 0,
        });

        return {
          success: false,
          documentId,
          pagesProcessed: 0,
          error: ocrResult.error || "OCR processing failed",
        };
      }
    } catch (error) {
      Logger.error("Error processing image document", {
        documentId,
        error,
      });

      return {
        success: false,
        documentId,
        pagesProcessed: 0,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }



  /**
   * Stores extracted images for reuse in "Cites to your documents" functionality
   * Uses the resources table to store image metadata without schema changes
   * @param documentId The document ID
   * @param pages The extracted pages with images
   * @param extractionMetadata The extraction metadata
   */
  private static async storeExtractedImages(
    documentId: string,
    pages: DocumentPage[],
    extractionMetadata: any
  ): Promise<void> {
    try {
      Logger.info("Storing extracted images for Cites to your documents reuse", {
        documentId,
        pageCount: pages.length,
        totalImageSize: Math.round(pages.reduce((sum, page) => sum + page.imageBase64.length, 0) / 1024) + 'KB',
      });

      // Prepare image data for storage (limit to first 3 pages to manage size)
      const maxPagesForStorage = 3;
      const imagesToStore = pages.slice(0, maxPagesForStorage).map((page, index) => ({
        pageNumber: page.pageNumber || (index + 1),
        imageBase64: page.imageBase64,
        extractedAt: new Date().toISOString(),
      }));

      // Create a special resource entry to store image metadata
      const imageMetadata = {
        type: 'extracted-images',
        images: imagesToStore,
        extractionMethod: extractionMetadata.extractionMethod,
        modeAgnostic: extractionMetadata.modeAgnostic,
        toggleStateIndependent: extractionMetadata.toggleStateIndependent,
        qualityValidated: extractionMetadata.qualityValidated,
        extractedAt: new Date().toISOString(),
        totalPages: pages.length,
        storedPages: imagesToStore.length,
        optimizedFlow: 'extract-once-store-reuse',
      };

      // Check if image metadata resource already exists
      const existingImageResource = await db
        .select({ id: resources.id })
        .from(resources)
        .where(
          and(
            eq(resources.sourceDocumentId, documentId),
            sql`${resources.content} LIKE '%"type":"extracted-images"%'`
          )
        )
        .limit(1);

      if (existingImageResource.length > 0) {
        // Update existing resource
        await db
          .update(resources)
          .set({
            content: JSON.stringify(imageMetadata),
            updatedAt: new Date(),
          })
          .where(eq(resources.id, existingImageResource[0].id));

        Logger.info("Updated existing extracted images resource", {
          documentId,
          resourceId: existingImageResource[0].id,
          storedPages: imagesToStore.length,
        });
      } else {
        // Create new resource for storing images
        await db
          .insert(resources)
          .values({
            content: JSON.stringify(imageMetadata),
            sourceDocumentId: documentId,
            createdAt: new Date(),
            updatedAt: new Date(),
          });

        Logger.info("Created new extracted images resource", {
          documentId,
          storedPages: imagesToStore.length,
          totalPages: pages.length,
        });
      }

    } catch (error) {
      Logger.error("Error storing extracted images", {
        documentId,
        error,
        pageCount: pages.length,
      });
      // Don't throw error to avoid breaking the main processing flow
    }
  }

  /**
   * Retrieves stored extracted images for "Cites to your documents" functionality
   * @param documentIds Array of document IDs to retrieve images for
   * @returns Array of images in the format expected by the API
   */
  static async getStoredImagesForCitedDocuments(
    documentIds: string[]
  ): Promise<Array<{ mime_type: string; url: string; documentId: string; pageNumber: number }>> {
    try {
      Logger.info("Retrieving stored images for Cites to your documents", {
        documentIds,
        documentCount: documentIds.length,
      });

      if (documentIds.length === 0) {
        return [];
      }

      // Get image resources for the specified documents
      const imageResources = await db
        .select({
          sourceDocumentId: resources.sourceDocumentId,
          content: resources.content,
        })
        .from(resources)
        .where(
          and(
            sql`${resources.sourceDocumentId} = ANY(${documentIds})`,
            sql`${resources.content} LIKE '%"type":"extracted-images"%'`
          )
        );

      const allImages: Array<{ mime_type: string; url: string; documentId: string; pageNumber: number }> = [];

      for (const resource of imageResources) {
        try {
          const imageMetadata = JSON.parse(resource.content);

          if (imageMetadata.type === 'extracted-images' && imageMetadata.images && Array.isArray(imageMetadata.images)) {
            Logger.info("Found stored images for document", {
              documentId: resource.sourceDocumentId,
              storedPages: imageMetadata.images.length,
              extractionMethod: imageMetadata.extractionMethod,
              optimizedFlow: imageMetadata.optimizedFlow,
            });

            // Convert stored images to API format
            const documentImages = imageMetadata.images.map((image: any) => ({
              mime_type: "image/jpeg",
              url: image.imageBase64, // Already in data URL format
              documentId: resource.sourceDocumentId,
              pageNumber: image.pageNumber,
            }));

            allImages.push(...documentImages);
          } else {
            Logger.warn("Invalid image metadata format for document", {
              documentId: resource.sourceDocumentId,
              metadataType: imageMetadata.type,
              hasImages: !!imageMetadata.images,
            });
          }
        } catch (error) {
          Logger.error("Error parsing stored image metadata for document", {
            documentId: resource.sourceDocumentId,
            error,
          });
        }
      }

      Logger.info("Retrieved stored images for Cites to your documents", {
        documentIds,
        totalImages: allImages.length,
        resourcesFound: imageResources.length,
      });

      return allImages;

    } catch (error) {
      Logger.error("Error retrieving stored images for Cites to your documents", {
        documentIds,
        error,
      });
      return [];
    }
  }

  /**
   * Stores the generated markdown in the database and processes it for inline citations
   * @param documentId The document ID
   * @param markdown The markdown content
   */
  private static async storeMarkdownInDatabase(
    documentId: string,
    markdown: string
  ): Promise<void> {
    try {
      Logger.info("DEBUG: Starting markdown storage process", {
        documentId,
        markdownLength: markdown.length,
        markdownPreview: markdown.substring(0, 200),
      });

      // Store markdown in database
      await db
        .update(sourceDocuments)
        .set({ extractedText: markdown })
        .where(eq(sourceDocuments.id, documentId));

      Logger.info("DEBUG: Markdown stored in database successfully", {
        documentId,
        markdownLength: markdown.length,
      });

      // Process markdown for inline citations chunking and embedding
      Logger.info("DEBUG: Starting inline citations processing", {
        documentId,
      });

      await this.processMarkdownForInlineCitations(documentId, markdown);

      Logger.info("DEBUG: Inline citations processing completed", {
        documentId,
      });
    } catch (error) {
      Logger.error("DEBUG: Error in markdown storage/processing pipeline", {
        documentId,
        error,
        markdownLength: markdown?.length || 0,
      });
      throw error;
    }
  }

  /**
   * Processes markdown for inline citations: chunking and embedding
   * @param documentId The document ID
   * @param markdown The markdown content
   */
  private static async processMarkdownForInlineCitations(
    documentId: string,
    markdown: string
  ): Promise<void> {
    try {
      Logger.info("DEBUG: Starting inline citations markdown processing", {
        documentId,
        markdownLength: markdown.length,
        markdownWordCount: markdown.split(/\s+/).length,
        markdownLineCount: markdown.split('\n').length,
      });

      // DEBUG: Log markdown structure analysis
      const headings = markdown.match(/^#+\s+.+$/gm) || [];
      const tables = markdown.match(/\|.*\|/g) || [];
      Logger.info("DEBUG: Markdown structure analysis", {
        documentId,
        headingCount: headings.length,
        tableRowCount: tables.length,
        firstFewHeadings: headings.slice(0, 3),
      });

      // Step 1: Chunk the markdown
      Logger.info("DEBUG: Starting chunking process", { documentId });
      const chunkingResult = await InlineCitationsChunker.chunkMarkdownForInlineCitations(
        markdown,
        documentId
      );

      Logger.info("DEBUG: Chunking process completed", {
        documentId,
        success: chunkingResult.success,
        totalChunks: chunkingResult.totalChunks,
        error: chunkingResult.error,
      });

      if (!chunkingResult.success) {
        Logger.error("DEBUG: Markdown chunking failed for inline citations", {
          documentId,
          error: chunkingResult.error,
          markdownLength: markdown.length,
        });
        return;
      }

      Logger.info("Markdown chunking completed for inline citations", {
        documentId,
        totalChunks: chunkingResult.totalChunks,
        totalTokens: chunkingResult.totalTokens,
        averageChunkSize: chunkingResult.averageChunkSize,
      });

      // DEBUG: Log sample chunks
      if (chunkingResult.chunks.length > 0) {
        const sampleChunks = chunkingResult.chunks.slice(0, 2).map(chunk => ({
          id: chunk.id,
          textPreview: chunk.text.substring(0, 100),
          textLength: chunk.text.length,
          metadata: chunk.metadata,
        }));
        Logger.info("DEBUG: Sample chunks created", {
          documentId,
          sampleChunks,
        });
      }

      // Step 2: Generate embeddings and store chunks
      Logger.info("DEBUG: Starting embedding generation", {
        documentId,
        chunkCount: chunkingResult.chunks.length,
      });

      if (chunkingResult.chunks.length > 0) {
        const embeddingResult = await OpenAIEmbeddingService.generateAndStoreEmbeddings(
          chunkingResult.chunks
        );

        Logger.info("DEBUG: Embedding generation completed", {
          documentId,
          success: embeddingResult.success,
          processedChunks: embeddingResult.processedChunks,
          totalEmbeddings: embeddingResult.totalEmbeddings,
          error: embeddingResult.error,
        });

        if (embeddingResult.success) {
          Logger.info("Inline citations embedding and storage completed", {
            documentId,
            processedChunks: embeddingResult.processedChunks,
            totalEmbeddings: embeddingResult.totalEmbeddings,
            averageVectorLength: embeddingResult.averageVectorLength,
          });
        } else {
          Logger.error("DEBUG: Embedding generation failed for inline citations", {
            documentId,
            error: embeddingResult.error,
            chunkCount: chunkingResult.chunks.length,
          });
        }
      } else {
        Logger.warn("DEBUG: No chunks generated for inline citations", {
          documentId,
          markdownLength: markdown.length,
          chunkingSuccess: chunkingResult.success,
        });
      }
    } catch (error) {
      Logger.error("Error in inline citations markdown processing", {
        documentId,
        error,
      });
      // Don't throw error to avoid breaking the main document processing flow
    }
  }


}
