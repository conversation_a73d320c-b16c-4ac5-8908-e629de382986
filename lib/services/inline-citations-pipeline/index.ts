/**
 * Inline Citations Pipeline - Main Entry Point
 * 
 * This module provides a modular, reusable inline citations pipeline
 * that can be used for document processing and citation generation.
 * 
 * Usage Examples:
 * 
 * 1. Document Processing:
 * ```typescript
 * const pipeline = new InlineCitationsPipeline();
 * const result = await pipeline.processDocument({
 *   documentId: 'doc123',
 *   url: 'https://example.com/document.pdf',
 *   contentType: 'application/pdf'
 * });
 * ```
 * 
 * 2. Citation Generation:
 * ```typescript
 * const pipeline = new InlineCitationsPipeline();
 * const result = await pipeline.generateCitations({
 *   messageId: 'msg123',
 *   chatId: 'chat123',
 *   content: 'User message content...'
 * });
 * ```
 * 
 * 3. Custom Configuration:
 * ```typescript
 * const pipeline = new InlineCitationsPipeline({
 *   pdf: { scaleFactor: 2.0, maxPages: 100 },
 *   embedding: { provider: 'openai', model: 'text-embedding-3-small' }
 * });
 * ```
 */

import { Logger } from "../../utils/Logger";
import { InlineCitationsPipelineOrchestrator, PipelineExecutionOptions } from "./pipeline-orchestrator";
import { PipelineConfig, createPipelineConfig, DeepPartial } from "./config";
import { PipelineResult, PipelineContext, PipelineEvent } from "./types";

// Re-export types and interfaces for external use
export * from "./types";
export type { PipelineConfig } from "./config";
export type { DeepPartial } from "./config";
export { createPipelineConfig, DEFAULT_PIPELINE_CONFIG } from "./config";
export { InlineCitationsPipelineOrchestrator } from "./pipeline-orchestrator";

// Re-export individual modules for advanced usage
export { PdfProcessingModule } from "./modules/pdf-processing-module";
export { OcrProcessingModule } from "./modules/ocr-processing-module";
export { MarkdownStorageModule } from "./modules/markdown-storage-module";
export { ChunkingModule } from "./modules/chunking-module";
export { EmbeddingModule } from "./modules/embedding-module";
export { CitationTriggerModule } from "./modules/citation-trigger-module";
export { CitationGenerationModule } from "./modules/citation-generation-module";
export { PerformanceModule } from "./modules/performance-module";

/**
 * Main Pipeline Class - Simplified interface for common use cases
 */
export class InlineCitationsPipeline {
  private orchestrator: InlineCitationsPipelineOrchestrator;
  private config: PipelineConfig;

  constructor(configOverrides?: DeepPartial<PipelineConfig>) {
    this.config = createPipelineConfig(configOverrides);
    this.orchestrator = new InlineCitationsPipelineOrchestrator(this.config);

    Logger.info("Inline Citations Pipeline initialized", {
      version: "1.0.0",
      config: {
        pdfProvider: "server-side-pdfjs",
        ocrProvider: this.config.ocr.model,
        embeddingProvider: this.config.embedding.provider,
        embeddingModel: this.config.embedding.model,
      },
    });
  }

  /**
   * Process a document through the complete pipeline
   * (PDF → Images → OCR → Markdown → Storage → Chunking → Embeddings)
   */
  async processDocument(input: {
    documentId: string;
    url: string;
    contentType: string;
    userId?: string;
    sessionId?: string;
  }, options?: PipelineExecutionOptions): Promise<PipelineResult> {
    Logger.info("Starting document processing pipeline", {
      documentId: input.documentId,
      contentType: input.contentType,
    });

    const executionOptions: PipelineExecutionOptions = {
      enablePerformanceMonitoring: true,
      enableEventLogging: true,
      ...options,
      onlyStages: options?.onlyStages || [
        'pdf-processing',
        'ocr-processing', 
        'markdown-storage',
        'chunking',
        'embedding'
      ],
    };

    return await this.orchestrator.execute(input, {
      documentId: input.documentId,
      userId: input.userId,
      sessionId: input.sessionId,
    }, executionOptions);
  }

  /**
   * Generate citations for a message
   * (Trigger Evaluation → Citation Generation)
   */
  async generateCitations(input: {
    messageId: string;
    chatId: string;
    content: string;
    messages?: any[];
    chatExists?: boolean;
    isReadonly?: boolean;
    userId?: string;
    sessionId?: string;
  }, options?: PipelineExecutionOptions): Promise<PipelineResult> {
    Logger.info("Starting citation generation pipeline", {
      messageId: input.messageId,
      chatId: input.chatId,
      contentLength: input.content.length,
    });

    const executionOptions: PipelineExecutionOptions = {
      enablePerformanceMonitoring: true,
      enableEventLogging: true,
      ...options,
      onlyStages: options?.onlyStages || [
        'citation-trigger',
        'citation-generation'
      ],
    };

    return await this.orchestrator.execute({
      ...input,
      messages: input.messages || [],
      chatExists: input.chatExists || false,
      isReadonly: input.isReadonly || false,
    }, {
      messageId: input.messageId,
      chatId: input.chatId,
      userId: input.userId,
      sessionId: input.sessionId,
    }, executionOptions);
  }

  /**
   * Execute only the PDF processing stage
   */
  async processPdf(input: {
    documentId: string;
    url: string;
    contentType: string;
  }, options?: PipelineExecutionOptions): Promise<PipelineResult> {
    return await this.orchestrator.execute(input, {
      documentId: input.documentId,
    }, {
      ...options,
      onlyStages: ['pdf-processing'],
    });
  }

  /**
   * Execute only the OCR processing stage
   */
  async processOcr(input: {
    documentId: string;
    pages: any[];
  }, options?: PipelineExecutionOptions): Promise<PipelineResult> {
    return await this.orchestrator.execute(input, {
      documentId: input.documentId,
    }, {
      ...options,
      onlyStages: ['ocr-processing'],
    });
  }

  /**
   * Execute only the chunking and embedding stages
   */
  async processEmbeddings(input: {
    documentId: string;
    markdown: string;
    sourceType?: 'pdf' | 'docx' | 'txt' | 'image';
  }, options?: PipelineExecutionOptions): Promise<PipelineResult> {
    return await this.orchestrator.execute({
      ...input,
      sourceType: input.sourceType || 'pdf',
    }, {
      documentId: input.documentId,
    }, {
      ...options,
      onlyStages: ['chunking', 'embedding'],
    });
  }

  /**
   * Execute only the citation trigger evaluation
   */
  async evaluateCitationTrigger(input: {
    messageId: string;
    chatId: string;
    messages: any[];
    chatExists: boolean;
    isReadonly: boolean;
    content?: string;
  }, options?: PipelineExecutionOptions): Promise<PipelineResult> {
    return await this.orchestrator.execute(input, {
      messageId: input.messageId,
      chatId: input.chatId,
    }, {
      ...options,
      onlyStages: ['citation-trigger'],
    });
  }

  /**
   * Get pipeline execution status
   */
  async getExecutionStatus(requestId: string): Promise<PipelineContext | null> {
    return await this.orchestrator.getStatus(requestId);
  }

  /**
   * Add event listener for pipeline events
   */
  addEventListener(listener: (event: PipelineEvent) => void): void {
    this.orchestrator.addEventListener(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: PipelineEvent) => void): void {
    this.orchestrator.removeEventListener(listener);
  }

  /**
   * Get current pipeline configuration
   */
  getConfig(): PipelineConfig {
    return this.orchestrator.getConfig();
  }

  /**
   * Update pipeline configuration
   */
  updateConfig(updates: DeepPartial<PipelineConfig>): void {
    this.config = createPipelineConfig({ ...this.config, ...updates });
    this.orchestrator.updateConfig(this.config);
  }

  /**
   * Get health status of all pipeline modules
   */
  async getHealthStatus(): Promise<Record<string, { healthy: boolean; details?: any }>> {
    const modules = this.orchestrator.getModules();
    const healthStatuses: Record<string, { healthy: boolean; details?: any }> = {};

    for (const [name, pipelineModule] of Object.entries(modules)) {
      try {
        if (pipelineModule.getHealthStatus) {
          healthStatuses[name] = await pipelineModule.getHealthStatus();
        } else {
          healthStatuses[name] = { healthy: true, details: { note: 'Health check not implemented' } };
        }
      } catch (error) {
        healthStatuses[name] = {
          healthy: false,
          details: { error: error instanceof Error ? error.message : String(error) },
        };
      }
    }

    return healthStatuses;
  }

  /**
   * Get pipeline version and module information
   */
  getInfo(): {
    version: string;
    modules: Array<{ name: string; version: string; dependencies: string[] }>;
    config: PipelineConfig;
  } {
    const modules = this.orchestrator.getModules();
    
    return {
      version: "1.0.0",
      modules: Object.values(modules).map(pipelineModule => ({
        name: pipelineModule.name,
        version: pipelineModule.version,
        dependencies: pipelineModule.dependencies || [],
      })),
      config: this.config,
    };
  }
}

/**
 * Create a new pipeline instance with default configuration
 */
export function createPipeline(configOverrides?: DeepPartial<PipelineConfig>): InlineCitationsPipeline {
  return new InlineCitationsPipeline(configOverrides);
}

/**
 * Create a pipeline instance optimized for development
 * Uses Enhanced Doc Reader image extraction with debug options enabled
 */
export function createDevPipeline(): InlineCitationsPipeline {
  return new InlineCitationsPipeline({
    pdf: {
      // Enhanced Doc Reader settings - DO NOT MODIFY
      scaleFactor: 1.5,
      jpegQuality: 0.9,
      targetDpi: 150,
      // Development options
      saveDebugImages: true,
      maxPages: 10,
      debugImageDir: 'debug-images-dev',
    },
    ocr: {
      saveDebugMarkdown: true,
    },
    performance: {
      logLevel: 'debug',
      enableMetrics: true,
      enableTiming: true,
    },
  });
}

/**
 * Create a pipeline instance optimized for production
 * Uses Enhanced Doc Reader image extraction with production settings
 */
export function createProductionPipeline(): InlineCitationsPipeline {
  return new InlineCitationsPipeline({
    pdf: {
      // Enhanced Doc Reader settings - DO NOT MODIFY
      scaleFactor: 1.5,
      jpegQuality: 0.9,
      targetDpi: 150,
      // Production options
      saveDebugImages: false,
      maxPages: 50,
      debugImageDir: 'debug-images',
    },
    ocr: {
      saveDebugMarkdown: false,
    },
    performance: {
      logLevel: 'info',
      enableMetrics: true,
      enableMemoryTracking: true,
    },
  });
}
